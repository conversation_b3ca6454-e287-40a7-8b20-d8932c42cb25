<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教师主页 - 特靠谱教培系统</title>
    <!-- Bootstrap CSS -->
    <link href="../../web/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../web/css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .feature-card {
            text-align: center;
            padding: 30px 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .feature-card:hover {
            background-color: #f8f9fa;
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #28a745;
        }
        .schedule-item {
            border-left: 4px solid #28a745;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
            border-radius: 0 8px 8px 0;
        }
        .schedule-time {
            font-weight: bold;
            color: #28a745;
        }
        .schedule-subject {
            font-size: 1.1rem;
            margin: 5px 0;
        }
        .schedule-students {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .quick-stats {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .leave-request {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            background: white;
            transition: all 0.3s;
        }
        .leave-request:hover {
            border-color: #28a745;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15);
        }
        .leave-status {
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 12px;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        .btn-approve {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
            border-radius: 20px;
            padding: 5px 15px;
            font-size: 0.8rem;
        }
        .btn-reject {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
            border-radius: 20px;
            padding: 5px 15px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.html">
                            <i class="fa fa-home"></i> 主页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="schedule.html">
                            <i class="fa fa-calendar"></i> 课表
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="leave-approval.html">
                            <i class="fa fa-file-text"></i> 请假审批
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-user"></i> 李老师
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fa fa-user"></i> 个人信息</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fa fa-cog"></i> 设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../index.html"><i class="fa fa-sign-out"></i> 退出</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 欢迎信息 -->
        <div class="row">
            <div class="col-12">
                <div class="quick-stats">
                    <h4><i class="fa fa-chalkboard-teacher"></i> 欢迎回来，李老师！</h4>
                    <p class="mb-0">今天是 <span id="currentDate"></span>，祝您工作顺利！</p>
                </div>
            </div>
        </div>

        <!-- 快速统计 -->
        <div class="row mb-4">
            <div class="col-md-3 col-6 mb-3">
                <div class="card">
                    <div class="card-body stat-item">
                        <div class="stat-number text-success">8</div>
                        <div class="stat-label">本周课程</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="card">
                    <div class="card-body stat-item">
                        <div class="stat-number text-primary">25</div>
                        <div class="stat-label">学生总数</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="card">
                    <div class="card-body stat-item">
                        <div class="stat-number text-warning">3</div>
                        <div class="stat-label">待审批请假</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="card">
                    <div class="card-body stat-item">
                        <div class="stat-number text-info">92%</div>
                        <div class="stat-label">平均出勤率</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 今日课程 -->
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-calendar-day"></i> 今日课程</h5>
                    </div>
                    <div class="card-body">
                        <div class="schedule-item">
                            <div class="schedule-time">09:00 - 10:30</div>
                            <div class="schedule-subject">数学 - 代数基础</div>
                            <div class="schedule-students">
                                <i class="fa fa-users"></i> 12名学生 | <i class="fa fa-map-marker"></i> 教室A101
                                <span class="float-end">
                                    <button class="btn btn-sm btn-outline-success" onclick="startClass('数学')">
                                        <i class="fa fa-play"></i> 开始上课
                                    </button>
                                </span>
                            </div>
                        </div>
                        <div class="schedule-item">
                            <div class="schedule-time">14:00 - 15:30</div>
                            <div class="schedule-subject">数学 - 几何基础</div>
                            <div class="schedule-students">
                                <i class="fa fa-users"></i> 8名学生 | <i class="fa fa-map-marker"></i> 教室A102
                                <span class="float-end">
                                    <button class="btn btn-sm btn-outline-success" onclick="startClass('数学')">
                                        <i class="fa fa-play"></i> 开始上课
                                    </button>
                                </span>
                            </div>
                        </div>
                        <div class="schedule-item">
                            <div class="schedule-time">16:00 - 17:30</div>
                            <div class="schedule-subject">数学 - 综合练习</div>
                            <div class="schedule-students">
                                <i class="fa fa-users"></i> 15名学生 | <i class="fa fa-map-marker"></i> 教室A101
                                <span class="float-end">
                                    <button class="btn btn-sm btn-outline-success" onclick="startClass('数学')">
                                        <i class="fa fa-play"></i> 开始上课
                                    </button>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速功能 -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-bolt"></i> 快速功能</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="feature-card" onclick="location.href='schedule.html'">
                            <div class="feature-icon">
                                <i class="fa fa-calendar"></i>
                            </div>
                            <h6>查看课表</h6>
                            <p class="text-muted mb-0">查看完整教学安排</p>
                        </div>
                        <hr class="my-0">
                        <div class="feature-card" onclick="location.href='leave-approval.html'">
                            <div class="feature-icon">
                                <i class="fa fa-file-text"></i>
                            </div>
                            <h6>请假审批</h6>
                            <p class="text-muted mb-0">处理学生请假申请</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 待处理请假申请 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fa fa-clock"></i> 待处理请假申请</h5>
                        <a href="leave-approval.html" class="btn btn-sm btn-outline-light">
                            <i class="fa fa-list"></i> 查看全部
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="leave-request">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <strong>张同学 - 数学课请假</strong>
                                        <span class="leave-status status-pending">待审批</span>
                                    </div>
                                    <div class="text-muted small">
                                        <div>课程时间：2025-01-22 09:00-10:30</div>
                                        <div>申请时间：2025-01-21 10:30</div>
                                        <div>请假原因：身体不适，需要看医生</div>
                                        <div>联系方式：138****5678</div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <button class="btn btn-approve me-2" onclick="approveLeave('张同学', '数学')">
                                        <i class="fa fa-check"></i> 批准
                                    </button>
                                    <button class="btn btn-reject" onclick="rejectLeave('张同学', '数学')">
                                        <i class="fa fa-times"></i> 拒绝
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="leave-request">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <strong>王同学 - 数学课请假</strong>
                                        <span class="leave-status status-pending">待审批</span>
                                    </div>
                                    <div class="text-muted small">
                                        <div>课程时间：2025-01-22 14:00-15:30</div>
                                        <div>申请时间：2025-01-21 09:15</div>
                                        <div>请假原因：家庭事务，需要陪同家长办事</div>
                                        <div>联系方式：139****1234</div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <button class="btn btn-approve me-2" onclick="approveLeave('王同学', '数学')">
                                        <i class="fa fa-check"></i> 批准
                                    </button>
                                    <button class="btn btn-reject" onclick="rejectLeave('王同学', '数学')">
                                        <i class="fa fa-times"></i> 拒绝
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="leave-request">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <strong>李同学 - 数学课请假</strong>
                                        <span class="leave-status status-pending">待审批</span>
                                    </div>
                                    <div class="text-muted small">
                                        <div>课程时间：2025-01-23 16:00-17:30</div>
                                        <div>申请时间：2025-01-21 08:45</div>
                                        <div>请假原因：学校有其他活动安排</div>
                                        <div>联系方式：137****9876</div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <button class="btn btn-approve me-2" onclick="approveLeave('李同学', '数学')">
                                        <i class="fa fa-check"></i> 批准
                                    </button>
                                    <button class="btn btn-reject" onclick="rejectLeave('李同学', '数学')">
                                        <i class="fa fa-times"></i> 拒绝
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 审批确认模态框 -->
    <div class="modal fade" id="approvalModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="approvalModalTitle">请假审批</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="approvalModalBody">
                    <!-- 审批内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmApproval">确认</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../web/js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../web/bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 显示当前日期
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });

        // 开始上课
        function startClass(subject) {
            alert(`开始${subject}课程，系统将记录出勤情况`);
        }

        // 批准请假
        function approveLeave(student, subject) {
            document.getElementById('approvalModalTitle').textContent = '批准请假';
            document.getElementById('approvalModalBody').innerHTML = `
                <p>确认批准 <strong>${student}</strong> 的 <strong>${subject}</strong> 课请假申请吗？</p>
                <div class="mb-3">
                    <label class="form-label">审批意见（可选）：</label>
                    <textarea class="form-control" id="approvalComment" rows="3" placeholder="请输入审批意见..."></textarea>
                </div>
            `;
            
            document.getElementById('confirmApproval').onclick = function() {
                const comment = document.getElementById('approvalComment').value;
                console.log(`批准 ${student} 的 ${subject} 课请假，意见：${comment}`);
                alert('请假申请已批准');
                bootstrap.Modal.getInstance(document.getElementById('approvalModal')).hide();
                // 这里应该调用API更新请假状态
            };
            
            new bootstrap.Modal(document.getElementById('approvalModal')).show();
        }

        // 拒绝请假
        function rejectLeave(student, subject) {
            document.getElementById('approvalModalTitle').textContent = '拒绝请假';
            document.getElementById('approvalModalBody').innerHTML = `
                <p>确认拒绝 <strong>${student}</strong> 的 <strong>${subject}</strong> 课请假申请吗？</p>
                <div class="mb-3">
                    <label class="form-label">拒绝原因：</label>
                    <textarea class="form-control" id="rejectReason" rows="3" placeholder="请输入拒绝原因..." required></textarea>
                </div>
            `;
            
            document.getElementById('confirmApproval').onclick = function() {
                const reason = document.getElementById('rejectReason').value;
                if (!reason.trim()) {
                    alert('请输入拒绝原因');
                    return;
                }
                console.log(`拒绝 ${student} 的 ${subject} 课请假，原因：${reason}`);
                alert('请假申请已拒绝');
                bootstrap.Modal.getInstance(document.getElementById('approvalModal')).hide();
                // 这里应该调用API更新请假状态
            };
            
            new bootstrap.Modal(document.getElementById('approvalModal')).show();
        }
    </script>
</body>
</html>
