<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员主页 - 特靠谱教培系统</title>
    <!-- Bootstrap CSS -->
    <link href="../../web/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../web/css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .feature-card {
            text-align: center;
            padding: 30px 20px;
            cursor: pointer;
            transition: all 0.3s;
            height: 100%;
        }
        .feature-card:hover {
            background-color: #f8f9fa;
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #6f42c1;
        }
        .quick-stats {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .recent-activity {
            max-height: 400px;
            overflow-y: auto;
        }
        .activity-item {
            border-left: 4px solid #6f42c1;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
            border-radius: 0 8px 8px 0;
        }
        .activity-time {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .activity-content {
            margin-top: 5px;
        }
        .system-status {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .status-online {
            background-color: #28a745;
        }
        .status-warning {
            background-color: #ffc107;
        }
        .status-offline {
            background-color: #dc3545;
        }
        .chart-container {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
            border-radius: 10px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.html">
                            <i class="fa fa-home"></i> 主页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.html">
                            <i class="fa fa-users"></i> 用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.html">
                            <i class="fa fa-book"></i> 课程管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classrooms.html">
                            <i class="fa fa-building"></i> 教室管理
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-user-shield"></i> 管理员
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fa fa-user"></i> 个人信息</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fa fa-cog"></i> 系统设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../index.html"><i class="fa fa-sign-out"></i> 退出</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 欢迎信息 -->
        <div class="row">
            <div class="col-12">
                <div class="quick-stats">
                    <h4><i class="fa fa-user-shield"></i> 欢迎回来，管理员！</h4>
                    <p class="mb-0">今天是 <span id="currentDate"></span>，系统运行正常</p>
                </div>
            </div>
        </div>

        <!-- 快速统计 -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body stat-item">
                        <div class="stat-number text-primary">156</div>
                        <div class="stat-label">学生总数</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body stat-item">
                        <div class="stat-number text-success">12</div>
                        <div class="stat-label">教师总数</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body stat-item">
                        <div class="stat-number text-warning">45</div>
                        <div class="stat-label">课程总数</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body stat-item">
                        <div class="stat-number text-info">8</div>
                        <div class="stat-label">教室总数</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 快速功能 -->
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-bolt"></i> 快速功能</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="row g-0">
                            <div class="col-md-6">
                                <div class="feature-card" onclick="location.href='users.html'">
                                    <div class="feature-icon">
                                        <i class="fa fa-users"></i>
                                    </div>
                                    <h6>用户管理</h6>
                                    <p class="text-muted mb-0">管理学生和教师账户</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-card" onclick="location.href='courses.html'">
                                    <div class="feature-icon">
                                        <i class="fa fa-book"></i>
                                    </div>
                                    <h6>课程管理</h6>
                                    <p class="text-muted mb-0">创建和管理课程</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-card" onclick="location.href='classrooms.html'">
                                    <div class="feature-icon">
                                        <i class="fa fa-building"></i>
                                    </div>
                                    <h6>教室管理</h6>
                                    <p class="text-muted mb-0">管理教室资源</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-card" onclick="showReports()">
                                    <div class="feature-icon">
                                        <i class="fa fa-chart-bar"></i>
                                    </div>
                                    <h6>数据报表</h6>
                                    <p class="text-muted mb-0">查看统计报表</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统状态 -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-server"></i> 系统状态</h5>
                    </div>
                    <div class="card-body">
                        <div class="system-status">
                            <div class="status-indicator status-online"></div>
                            <div>
                                <strong>数据库服务</strong><br>
                                <small class="text-muted">运行正常</small>
                            </div>
                        </div>
                        <div class="system-status">
                            <div class="status-indicator status-online"></div>
                            <div>
                                <strong>Web服务</strong><br>
                                <small class="text-muted">运行正常</small>
                            </div>
                        </div>
                        <div class="system-status">
                            <div class="status-indicator status-warning"></div>
                            <div>
                                <strong>微信服务</strong><br>
                                <small class="text-muted">连接缓慢</small>
                            </div>
                        </div>
                        <div class="system-status">
                            <div class="status-indicator status-online"></div>
                            <div>
                                <strong>文件存储</strong><br>
                                <small class="text-muted">运行正常</small>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="text-center">
                            <button class="btn btn-outline-primary btn-sm" onclick="checkSystemHealth()">
                                <i class="fa fa-refresh"></i> 检查系统健康
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 最近活动 -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-history"></i> 最近活动</h5>
                    </div>
                    <div class="card-body">
                        <div class="recent-activity">
                            <div class="activity-item">
                                <div class="activity-time">2025-01-21 14:30</div>
                                <div class="activity-content">
                                    <strong>新用户注册：</strong>张同学完成注册
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-time">2025-01-21 13:45</div>
                                <div class="activity-content">
                                    <strong>课程创建：</strong>李老师创建了"高等数学"课程
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-time">2025-01-21 12:20</div>
                                <div class="activity-content">
                                    <strong>请假审批：</strong>王老师批准了学生请假申请
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-time">2025-01-21 11:15</div>
                                <div class="activity-content">
                                    <strong>教室预约：</strong>教室A101被预约用于数学课
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-time">2025-01-21 10:30</div>
                                <div class="activity-content">
                                    <strong>系统备份：</strong>数据库自动备份完成
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-time">2025-01-21 09:45</div>
                                <div class="activity-content">
                                    <strong>用户登录：</strong>李老师通过微信登录系统
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据统计图表 -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-chart-line"></i> 数据统计</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <div class="text-center">
                                <i class="fa fa-chart-bar fa-3x mb-3"></i>
                                <p>统计图表区域</p>
                                <small class="text-muted">这里可以集成Chart.js或其他图表库<br>显示学生出勤率、课程统计等数据</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 待处理事项 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-tasks"></i> 待处理事项</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card border-warning">
                                    <div class="card-body text-center">
                                        <h5 class="text-warning">5</h5>
                                        <p class="mb-0">待审核教师申请</p>
                                        <button class="btn btn-outline-warning btn-sm mt-2" onclick="location.href='users.html'">
                                            查看详情
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <h5 class="text-info">12</h5>
                                        <p class="mb-0">课程冲突需要解决</p>
                                        <button class="btn btn-outline-info btn-sm mt-2" onclick="location.href='courses.html'">
                                            查看详情
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <h5 class="text-success">3</h5>
                                        <p class="mb-0">教室维护申请</p>
                                        <button class="btn btn-outline-success btn-sm mt-2" onclick="location.href='classrooms.html'">
                                            查看详情
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../web/js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../web/bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 显示当前日期
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });

        // 检查系统健康
        function checkSystemHealth() {
            alert('正在检查系统健康状态...\n\n数据库: 正常\nWeb服务: 正常\n微信服务: 连接缓慢\n文件存储: 正常');
        }

        // 显示报表
        function showReports() {
            alert('跳转到数据报表页面');
        }
    </script>
</body>
</html>
